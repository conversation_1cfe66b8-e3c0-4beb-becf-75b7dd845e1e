const { getOSClient } = require("../utils/connection");
const { config } = require("../environment/index");

const indexName = config.INDEX.targets

async function getLatestTargetById(userId, targetId) {
  const client = getOSClient();
  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ "createdAt": { order: "desc" } }],
      query: {
        bool: {
          must: [{ match: { targetId } }, { match: { "userId.keyword": userId } }],
        },
      },
      size: 1,
    },
  });

  return response.body?.hits?.hits[0]?._source || null
}

module.exports = {
  getLatestTargetById,
}
