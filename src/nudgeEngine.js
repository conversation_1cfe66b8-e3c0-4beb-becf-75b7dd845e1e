class NudgeRule {
  constructor(ruleId, description) {
    this.ruleId = ruleId;
    this.description = description;
  }

  applies(userData) {
    throw new Error("'applies()' must be implemented by subclass");
  }

  generateNudge(userData) {
    throw new Error("'generateNudge()' must be implemented by subclass");
  }
}

class NudgeEngine {
  constructor(rules = []) {
    this.rules = rules;
  }

  evaluate(userData) {
    const nudges = [];
    const context = { ...userData.data, userId: userData.userId };
    for (const rule of this.rules) {
      if (rule.applies({ data: context })) {
        // If Nudge exists by meeting conditions
        // Then generate the Nudge
        const nudge = rule.generateNudge({ data: context });
        nudges.push(nudge);
      }
    }
    return nudges;
  }
}

module.exports = { NudgeRule, NudgeEngine };
