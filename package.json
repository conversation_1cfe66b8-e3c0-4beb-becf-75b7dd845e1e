{"name": "trackernotifications", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "nodemon test/test.js"}, "repository": {"type": "git", "url": "git+https://github.com/20DegreesFit/TrackerNotifications.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/20DegreesFit/TrackerNotifications/issues"}, "homepage": "https://github.com/20DegreesFit/TrackerNotifications#readme", "dependencies": {"@aws-sdk/client-opensearch": "^3.609.0", "@aws-sdk/credential-provider-node": "^3.817.0", "@opensearch-project/opensearch": "^2.13.0", "aws-sdk": "^2.1654.0", "aws4": "^1.13.2", "bunyan": "^1.8.15", "bunyan-format": "^0.2.1", "fs": "^0.0.1-security", "lambda-local": "^2.2.0", "lodash": "^4.17.21", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "nconf": "^0.12.0", "nodemon": "^3.1.4", "path": "^0.12.7"}}