const { createClient } = require("./utils/connection");
const { computeTargetAchievement } = require("./service/target_achieved");
const { log } = require("./utils/logger");
const { getTimeRangeByDate } = require("./utils/helpers");
const { getUTCOffsetValue } = require("./service/userProfile")

const defaultHeaders = {
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept, X-Access-Token, x-api-key, X-Install-Type",
  "Access-Control-Allow-Origin": "*",
};

exports.handler = async (event) => {
  const body = JSON.parse(event.Records[0].body);
  log.info(`Received request body: ${JSON.stringify(body)}`);
  const { userId, date, trackerIdDeviceIdMapping, time } = body;
  if (!userId || !date || !trackerIdDeviceIdMapping || !time) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        success: false,
        message: "Required params missing",
        data: null,
      }),
      headers: defaultHeaders,
    };
  }
  await createClient();
  const response = {};
  
  const UTCOffsetMin = await getUTCOffsetValue(userId);
  const { startTime, endTime } = getTimeRangeByDate(UTCOffsetMin, date);
  log.info(`date: ${date} | UTCOffsetMin: ${UTCOffsetMin} | startTime: ${startTime}, endTime: ${endTime}`);
  
  for(const trackerId in trackerIdDeviceIdMapping){
    const deviceId = trackerIdDeviceIdMapping[trackerId];
    const data = await computeTargetAchievement(userId, trackerId, deviceId, date, startTime, endTime);
    response[trackerId] = data;
  }
  log.info(`Response received: ${JSON.stringify(response)}`);
  return {
    statusCode: 200,
    body: JSON.stringify({
      success: true,
      message: "Request processed successfully",
      data: response
    }),
    headers: defaultHeaders,
  };
};
