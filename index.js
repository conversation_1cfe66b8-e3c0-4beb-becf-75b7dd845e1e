const fs = require("fs");
const path = require("path");
const { NudgeEngine } = require("./src/nudgeEngine");
const { SleepMealTimingRule, StepTargetShortfallRule, InactivityNudgeRule } = require("./rules/index");
const { createOSClient, connectMongoDB } = require("./utils/connection");
const { computeTargetAchievement } = require("./service/target_achieved");
const { getLastMealLog } = require("./service/meallogs");
const { getAllDefaultTrackers } = require("./service/trackers");
const { log } = require("./utils/logger");
const { getTimeRangeByDate } = require("./utils/helpers");
const { getUTCOffsetValue } = require("./service/userProfile");
const { getLastLogByDeviceId, trackerMap } = require("./service/logs");

const activityTrackerId = 4;
const sleepTrackerId = 5;

const defaultHeaders = {
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept, X-Access-Token, x-api-key, X-Install-Type",
  "Access-Control-Allow-Origin": "*",
};

const ruleRegistry = {
  sleep_meal_gap: SleepMealTimingRule,
  step_target_shortfall: StepTargetShortfallRule,
  user_inactivity: InactivityNudgeRule,
};

async function loadRuleConfigs() {
  const configPath = path.resolve(
    __dirname,
    "config/nudgeRules.json"
  );
  const data = fs.readFileSync(configPath, "utf-8");
  return JSON.parse(data);
}

async function instantiateRules() {
  const configs = await loadRuleConfigs();
  return configs.filter((cfg) => cfg.enabled && ruleRegistry[cfg.ruleId]).map((cfg) => new ruleRegistry[cfg.ruleId](cfg.params));
}

exports.handler = async (event) => {
  const body = JSON.parse(event.Records[0].body);
  log.info(`Received request body: ${JSON.stringify(body)}`);
  const { userId, date, trackerIdDeviceIdMapping, time } = body;
  if (!userId || !date || !trackerIdDeviceIdMapping || !time) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        success: false,
        message: "Required params missing",
        data: null,
      }),
      headers: defaultHeaders,
    };
  }
  
  process.env['AWS_ACCESS_KEY_ID'] = "********************";
  process.env['AWS_SECRET_ACCESS_KEY'] = "BtH1XFJC4MNa2ntsTSpWo7sx1hKl/iW9ghqaIrm2";

  await createOSClient();
  await connectMongoDB();
  const response = {};

  const UTCOffsetMin = await getUTCOffsetValue(userId);
  const { startTime, endTime } = getTimeRangeByDate(UTCOffsetMin, date);
  log.info(`date: ${date} | UTCOffsetMin: ${UTCOffsetMin} | startTime: ${startTime}, endTime: ${endTime}`);
  
  for(const trackerId in trackerIdDeviceIdMapping){
    const deviceId = trackerIdDeviceIdMapping[trackerId];
    const data = await computeTargetAchievement(userId, trackerId, deviceId, date, startTime, endTime);
    response[trackerId] = data;
  }
  log.info(`Response received: ${JSON.stringify(response)}`);

  await calculateNudge(userId);
  log.info(`Nudge calculated for user: ${userId}`);

  return {
    statusCode: 200,
    body: JSON.stringify({
      success: true,
      message: "Request processed successfully",
      data: response
    }),
    headers: defaultHeaders,
  };
};

async function calculateNudge(userId) {
  const userData = { userId };
  const rules = await instantiateRules();
  const engine = new NudgeEngine(rules);

  // Loading default devices to fetch logs
  const defTrackers = await getAllDefaultTrackers(userId);
  const defaultDevices = defTrackers?.defaultDevices || [];
  const defaultDevicePerTrackerId = defaultDevices.reduce((acc, dev) => { acc[dev.trackerId] = dev.deviceId; return acc;}, {});

  const sleepLogs = await getLastLogByDeviceId(userId, trackerMap[sleepTrackerId].indexName , defaultDevicePerTrackerId[sleepTrackerId]);
  const mealLogs = await getLastMealLog(userId);
  const activityLogs = await getLastLogByDeviceId(userId, trackerMap[activityTrackerId].indexName , defaultDevicePerTrackerId[activityTrackerId]);

  userData.data = {
    sleepLogs,
    mealLogs,
    activityLogs,
  };
  // Pass user's logs data like sleep, meallog, steps to the engine for evaluation
  const nudges = engine.evaluate(userData);
  log.info(`Nudges received: ${JSON.stringify(nudges)}`);
  return nudges;
}
