/** 
 * Example usage:
 * roundOffNumber(3.14159, 2); // Output: 3.14 (as a number)
 * roundOffNumber("5.6789", 1); // Output: "5.7" (as a string)
 */
function roundOffNumber(num = 0, uptoDigits = 2) {
  num = parseFloat(num);
  const roundedNum = isNaN(num) ? 0 : num.toFixed(uptoDigits);
  return typeof num === 'string' ? roundedNum : parseFloat(roundedNum);
}

/**
 * @param {Number} UTCOffsetMin, offset value from UTC, in minutes e.g. 330 for India
 * @param {string} date, date e.g. "2024-07-14" 
 * @returns startTime & endTime as ISO strings for current day in that time zone
 */
function getTimeRangeByDate(UTCOffsetMin = 0, date = new Date().toISOString().split('T')[0]) {
  const offsetMilliseconds = UTCOffsetMin * 60 * 1000;
  const localMidnight = new Date(new Date(date).getTime() - offsetMilliseconds);
  const startTime = localMidnight.toISOString();
  const endTime = new Date(localMidnight.getTime() + (24 * 60 * 60 * 1000 - 1)).toISOString();
  const timeRange = { startTime, endTime };
  return timeRange;
}

function isEmpty(value) {
  if (typeof value === "object" && value !== null) {
    return Array.isArray(value) ? value.length === 0 : Object.keys(value).length === 0;
  }
  return false;
}

function checkIfAnyEmpty(obj) {
  for (let key in obj) {
    if (isEmpty(obj[key])) {
      return true; // Return true if any property is empty
    }
  }
  return false; // If none of the properties are empty, return false
}

module.exports = {
  checkIfAnyEmpty,
  roundOffNumber,
  getTimeRangeByDate,
};
